import { useState, useEffect, useCallback } from 'react';

export default function WeeklyActivityTimeline() {
  const [weeklyActivities, setWeeklyActivities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [lastRefresh, setLastRefresh] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(10); // minutes
  const [autoRefreshTimer, setAutoRefreshTimer] = useState(null);
  const [fetchingSummary, setFetchingSummary] = useState(null);

  // Fetch weekly activity data
  const fetchWeeklyActivity = useCallback(async () => {
    setLoading(true);
    setError('');

    try {
      console.log('🗓️ Fetching weekly activity timeline...');
      const response = await fetch('http://localhost:4000/api/weekly-activity-feed');
      const data = await response.json();

      if (response.ok) {
        setWeeklyActivities(data.weeklyActivities);
        setFetchingSummary(data.fetchingSummary);
        setLastRefresh(new Date(data.timestamp));
        console.log(`✅ Weekly activity loaded: ${data.fetchingSummary.totalActivitiesFetched} activities`);
      } else {
        setError(data.error || 'Failed to load weekly activity');
      }
    } catch (err) {
      console.error('❌ Error fetching weekly activity:', err);
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh functionality
  const startAutoRefresh = useCallback(() => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
    }

    const timer = setInterval(() => {
      console.log(`🔄 Auto-refreshing weekly activity (every ${refreshInterval} minutes)`);
      fetchWeeklyActivity();
    }, refreshInterval * 60 * 1000);

    setAutoRefreshTimer(timer);
  }, [refreshInterval, fetchWeeklyActivity]);

  const stopAutoRefresh = useCallback(() => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
      setAutoRefreshTimer(null);
    }
  }, [autoRefreshTimer]);

  // Handle auto-refresh toggle
  useEffect(() => {
    if (autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }

    return () => stopAutoRefresh(); // Cleanup on unmount
  }, [autoRefresh, startAutoRefresh, stopAutoRefresh]);

  // Initial load
  useEffect(() => {
    fetchWeeklyActivity();
  }, [fetchWeeklyActivity]);

  // Listen for external refresh events (from App.jsx auto-refresh)
  useEffect(() => {
    const handleRefreshEvent = () => {
      console.log('🔄 External refresh triggered for weekly activity');
      fetchWeeklyActivity();
    };

    window.addEventListener('refreshWeeklyActivity', handleRefreshEvent);
    return () => window.removeEventListener('refreshWeeklyActivity', handleRefreshEvent);
  }, [fetchWeeklyActivity]);

  // Get current week range for display
  const getCurrentWeekRange = () => {
    const now = new Date();
    const currentDay = now.getDay();
    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1;
    const mondayDate = new Date(now);
    mondayDate.setDate(now.getDate() - daysFromMonday);
    
    return {
      start: mondayDate.toLocaleDateString(),
      end: now.toLocaleDateString()
    };
  };

  // Combine all activities from all team members and sort chronologically
  const getCombinedWeeklyActivities = () => {
    const combinedActivities = [];

    weeklyActivities.forEach(memberData => {
      if (!memberData.error && memberData.activityFeed) {
        memberData.activityFeed.forEach(activity => {
          combinedActivities.push({
            ...activity,
            memberName: memberData.member,
            memberDisplayName: memberData.member
          });
        });
      }
    });

    // Sort by timestamp, most recent first
    return combinedActivities.sort((a, b) => new Date(b.published) - new Date(a.published));
  };

  const weekRange = getCurrentWeekRange();
  const combinedActivities = getCombinedWeeklyActivities();

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">🗓️ Weekly Activity Timeline</h2>
            <p className="text-gray-600 mt-1">
              Current week: {weekRange.start} - {weekRange.end} (Monday to today)
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Manual refresh button */}
            <button
              onClick={fetchWeeklyActivity}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:bg-gray-400 flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Loading...</span>
                </>
              ) : (
                <>
                  <span>🔄</span>
                  <span>Refresh</span>
                </>
              )}
            </button>

            {/* Auto-refresh controls */}
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Auto-refresh</span>
              </label>
              {autoRefresh && (
                <select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value={5}>5 min</option>
                  <option value={10}>10 min</option>
                  <option value={15}>15 min</option>
                  <option value={30}>30 min</option>
                </select>
              )}
            </div>
          </div>
        </div>

        {/* Status indicators */}
        <div className="flex items-center space-x-6 text-sm">
          {lastRefresh && (
            <div className="text-gray-600">
              Last updated: {lastRefresh.toLocaleString()}
            </div>
          )}
          {fetchingSummary && (
            <div className="text-gray-600">
              {fetchingSummary.totalActivitiesFetched} activities from {fetchingSummary.successfulFetches} team members
            </div>
          )}
          {autoRefresh && (
            <div className="text-green-600 font-medium">
              🔄 Auto-refreshing every {refreshInterval} minutes
            </div>
          )}
        </div>

        {/* Description */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
          <h3 className="font-semibold text-blue-900 mb-2">📋 Weekly Team Activity Overview</h3>
          <p className="text-blue-800 text-sm">
            This is your primary weekly dashboard showing all team member activities from Monday to today. 
            Perfect for weekly planning, standups, and delivery oversight. Activities are fetched from all 
            Valmet Jira projects where your team works.
          </p>
          {autoRefresh && (
            <p className="text-blue-700 text-sm mt-2">
              ✅ <strong>Auto-refresh enabled</strong> - Timeline stays current throughout the week.
            </p>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold mb-2">Error Loading Weekly Activity</h3>
          <p className="text-red-600 mb-3">{error}</p>
          <button
            onClick={fetchWeeklyActivity}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      )}

      {/* Combined Weekly Timeline */}
      {!loading && !error && (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-xl font-bold text-blue-900 flex items-center">
              🕐 This Week's Combined Activity Timeline
            </h3>
            <p className="text-blue-700 text-sm mt-1">
              Chronological view of all team activities from {weekRange.start} to {weekRange.end}
            </p>
          </div>

          <div className="p-6">
            {combinedActivities.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 text-lg mb-2">📭</div>
                <p className="text-gray-600">No activities found for this week yet.</p>
                <p className="text-gray-500 text-sm mt-1">
                  Activities will appear here as your team works on tickets throughout the week.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {combinedActivities.map((activity, index) => (
                  <div key={`${activity.memberName}-${activity.id}-${index}`} 
                       className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    {/* Member Avatar */}
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                        {activity.memberName.substring(0, 2).toUpperCase()}
                      </div>
                    </div>

                    {/* Activity Content */}
                    <div className="flex-grow min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900">{activity.memberDisplayName}</span>
                        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                          {activity.activityType}
                        </span>
                        {activity.ticketKey && (
                          <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                            {activity.ticketKey}
                          </span>
                        )}
                      </div>
                      
                      <div className="text-gray-800 text-sm leading-relaxed">
                        {activity.cleanTitle || activity.title}
                      </div>
                      
                      {activity.summary && (
                        <div className="text-gray-600 text-xs mt-1">
                          {activity.summary}
                        </div>
                      )}
                    </div>

                    {/* Timestamp */}
                    <div className="flex-shrink-0 text-right">
                      <div className="text-xs text-gray-500">
                        {new Date(activity.published).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        {new Date(activity.published).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
